resume_ranker_ai/
├── app.py                  # Main Streamlit application
├── requirements.txt        # Project dependencies
├── README.md               # Setup and usage instructions
├── utils/
│   ├── __init__.py
│   ├── document_parser.py  # PDF/DOCX parsing functionality
│   ├── nlp_analyzer.py     # NLP analysis and matching
│   └── recommender.py      # Recommendation generation
├── data/
│   ├── sample_resumes/     # Sample resumes for testing
│   └── sample_jobs/        # Sample job descriptions
└── tests/
    ├── __init__.py
    └── test_parser.py      # Basic tests